import { env } from "@/env";
import prisma from "@/lib/prisma";
import stripe from "@/lib/stripe";
import { auth, currentUser } from "@clerk/nextjs/server";
import { NextRequest } from "next/server";

export async function POST(req: NextRequest) {
  try {
    const { userId } = await auth();
    const user = await currentUser();

    if (!userId || !user) {
      return Response.json({ success: false, message: "Not authenticated" }, { status: 401 });
    }

    console.log("🔧 Manual sync requested for user:", userId);

    // Get Stripe customer ID from Clerk
    const stripeCustomerId = user.privateMetadata.stripeCustomerId as string | undefined;

    if (!stripeCustomerId) {
      // Try to find customer by email
      const customers = await stripe.customers.list({
        email: user.emailAddresses[0].emailAddress,
        limit: 1,
      });

      if (customers.data.length === 0) {
        return Response.json({ 
          success: false, 
          message: "No Stripe customer found. Please try subscribing again." 
        });
      }

      const customer = customers.data[0];
      console.log("📧 Found customer by email:", customer.id);

      // Update Clerk with the customer ID
      await (await import("@clerk/nextjs/server")).clerkClient().users.updateUserMetadata(userId, {
        privateMetadata: {
          stripeCustomerId: customer.id,
        },
      });

      // Now get subscriptions for this customer
      const subscriptions = await stripe.subscriptions.list({
        customer: customer.id,
        status: "all",
        limit: 10,
      });

      console.log("📋 Found subscriptions:", subscriptions.data.length);

      // Find active subscription
      const activeSubscription = subscriptions.data.find(
        sub => sub.status === "active" || sub.status === "trialing" || sub.status === "past_due"
      );

      if (activeSubscription) {
        console.log("✅ Found active subscription:", activeSubscription.id);

        // Create subscription in database
        const result = await prisma.userSubscription.upsert({
          where: { userId },
          create: {
            userId,
            stripeSubscriptionId: activeSubscription.id,
            stripeCustomerId: customer.id,
            stripePriceId: activeSubscription.items.data[0].price.id,
            stripeCurrentPeriodEnd: new Date(activeSubscription.current_period_end * 1000),
            stripeCancelAtPeriodEnd: activeSubscription.cancel_at_period_end,
          },
          update: {
            stripeSubscriptionId: activeSubscription.id,
            stripePriceId: activeSubscription.items.data[0].price.id,
            stripeCurrentPeriodEnd: new Date(activeSubscription.current_period_end * 1000),
            stripeCancelAtPeriodEnd: activeSubscription.cancel_at_period_end,
          },
        });

        console.log("💾 Subscription synced:", result);

        return Response.json({
          success: true,
          message: "Subscription synced successfully!",
          subscription: result,
        });
      } else {
        return Response.json({
          success: false,
          message: "No active subscription found in Stripe",
        });
      }
    } else {
      // We have a customer ID, use the existing refresh logic
      const subscriptions = await stripe.subscriptions.list({
        customer: stripeCustomerId,
        status: "all",
        limit: 10,
      });

      const activeSubscription = subscriptions.data.find(
        sub => sub.status === "active" || sub.status === "trialing" || sub.status === "past_due"
      );

      if (activeSubscription) {
        const result = await prisma.userSubscription.upsert({
          where: { userId },
          create: {
            userId,
            stripeSubscriptionId: activeSubscription.id,
            stripeCustomerId: stripeCustomerId,
            stripePriceId: activeSubscription.items.data[0].price.id,
            stripeCurrentPeriodEnd: new Date(activeSubscription.current_period_end * 1000),
            stripeCancelAtPeriodEnd: activeSubscription.cancel_at_period_end,
          },
          update: {
            stripeSubscriptionId: activeSubscription.id,
            stripePriceId: activeSubscription.items.data[0].price.id,
            stripeCurrentPeriodEnd: new Date(activeSubscription.current_period_end * 1000),
            stripeCancelAtPeriodEnd: activeSubscription.cancel_at_period_end,
          },
        });

        return Response.json({
          success: true,
          message: "Subscription synced successfully!",
          subscription: result,
        });
      } else {
        return Response.json({
          success: false,
          message: "No active subscription found",
        });
      }
    }
  } catch (error) {
    console.error("❌ Manual sync error:", error);
    return Response.json({
      success: false,
      message: `Error: ${error instanceof Error ? error.message : "Unknown error"}`,
    }, { status: 500 });
  }
}
