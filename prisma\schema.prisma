// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("POSTGRES_PRISMA_URL") // uses connection pooling
  directUrl = env("POSTGRES_URL_NON_POOLING") // uses a direct connection
}

model Resume {
  id     String @id @default(cuid())
  userId String

  title       String?
  description String?

  photoUrl    String?
  colorHex    String  @default("#000000")
  borderStyle String  @default("squircle")
  summary     String?
  firstName   String?
  lastName    String?
  jobTitle    String?
  city        String?
  country     String?
  phone       String?
  email       String?

  workExperiences WorkExperience[]
  educations      Education[]
  skills          String[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("resumes")
}

model WorkExperience {
  id String @id @default(cuid())

  position    String?
  company     String?
  startDate   DateTime?
  endDate     DateTime?
  description String?

  resumeId String
  resume   Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("work_experiences")
}

model Education {
  id String @id @default(cuid())

  degree    String?
  school    String?
  startDate DateTime?
  endDate   DateTime?

  resumeId String
  resume   Resume @relation(fields: [resumeId], references: [id], onDelete: Cascade)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("educations")
}

model UserSubscription {
  id                      String   @id @default(cuid())
  userId                  String   @unique
  stripeCustomerId        String   @unique
  stripeSubscriptionId    String   @unique
  stripePriceId           String
  stripeCurrentPeriodEnd  DateTime
  stripeCancelAtPeriodEnd Boolean  @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("user_subscriptions")
}
