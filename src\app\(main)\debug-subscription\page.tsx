import { env } from "@/env";
import prisma from "@/lib/prisma";
import { getUserSubscriptionLevel } from "@/lib/subscription";
import { auth } from "@clerk/nextjs/server";
import { formatDate } from "date-fns";
import RefreshSubscriptionButton from "./RefreshSubscriptionButton";

export default async function DebugSubscriptionPage() {
  const { userId } = await auth();

  if (!userId) {
    return <div>Not authenticated</div>;
  }

  // Get subscription data directly from database
  const subscription = await prisma.userSubscription.findUnique({
    where: { userId },
  });

  // Get subscription level using our function
  const subscriptionLevel = await getUserSubscriptionLevel(userId);

  return (
    <main className="mx-auto w-full max-w-4xl space-y-6 px-3 py-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Subscription Debug</h1>
        <RefreshSubscriptionButton />
      </div>

      <div className="space-y-4">
        <div className="rounded-lg border p-4">
          <h2 className="text-xl font-semibold mb-2">User Information</h2>
          <p><strong>User ID:</strong> {userId}</p>
        </div>

        <div className="rounded-lg border p-4">
          <h2 className="text-xl font-semibold mb-2">Environment Variables</h2>
          <p><strong>Pro Monthly Price ID:</strong> {env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_MONTHLY}</p>
          <p><strong>Pro Plus Monthly Price ID:</strong> {env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_PLUS_MONTHLY}</p>
        </div>

        <div className="rounded-lg border p-4">
          <h2 className="text-xl font-semibold mb-2">Computed Subscription Level</h2>
          <p className="text-lg">
            <strong>Level:</strong>
            <span className={`ml-2 px-2 py-1 rounded ${
              subscriptionLevel === 'free' ? 'bg-gray-200' :
              subscriptionLevel === 'pro' ? 'bg-blue-200' :
              'bg-purple-200'
            }`}>
              {subscriptionLevel}
            </span>
          </p>
        </div>

        <div className="rounded-lg border p-4">
          <h2 className="text-xl font-semibold mb-2">Database Subscription Data</h2>
          {subscription ? (
            <div className="space-y-2">
              <p><strong>Subscription ID:</strong> {subscription.id}</p>
              <p><strong>Stripe Customer ID:</strong> {subscription.stripeCustomerId}</p>
              <p><strong>Stripe Subscription ID:</strong> {subscription.stripeSubscriptionId}</p>
              <p><strong>Stripe Price ID:</strong> {subscription.stripePriceId}</p>
              <p><strong>Current Period End:</strong> {formatDate(subscription.stripeCurrentPeriodEnd, "PPP")}</p>
              <p><strong>Cancel at Period End:</strong> {subscription.stripeCancelAtPeriodEnd ? "Yes" : "No"}</p>
              <p><strong>Created At:</strong> {formatDate(subscription.createdAt, "PPP")}</p>
              <p><strong>Updated At:</strong> {formatDate(subscription.updatedAt, "PPP")}</p>

              <div className="mt-4 p-3 bg-gray-100 rounded">
                <h3 className="font-semibold">Price ID Comparison:</h3>
                <p>Database Price ID: <code>{subscription.stripePriceId}</code></p>
                <p>Pro Monthly Price ID: <code>{env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_MONTHLY}</code></p>
                <p>Pro Plus Monthly Price ID: <code>{env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_PLUS_MONTHLY}</code></p>
                <p>
                  <strong>Matches Pro:</strong> {subscription.stripePriceId === env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_MONTHLY ? "✅ Yes" : "❌ No"}
                </p>
                <p>
                  <strong>Matches Pro Plus:</strong> {subscription.stripePriceId === env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_PLUS_MONTHLY ? "✅ Yes" : "❌ No"}
                </p>
              </div>

              <div className="mt-4 p-3 bg-gray-100 rounded">
                <h3 className="font-semibold">Period Check:</h3>
                <p>Current Time: <code>{new Date().toISOString()}</code></p>
                <p>Period End: <code>{subscription.stripeCurrentPeriodEnd.toISOString()}</code></p>
                <p>
                  <strong>Is Active:</strong> {subscription.stripeCurrentPeriodEnd > new Date() ? "✅ Yes" : "❌ No"}
                </p>
              </div>
            </div>
          ) : (
            <p className="text-red-600">No subscription found in database</p>
          )}
        </div>

        <div className="rounded-lg border p-4">
          <h2 className="text-xl font-semibold mb-2">Troubleshooting Steps</h2>
          <ol className="list-decimal list-inside space-y-2">
            <li>Check if the subscription exists in the database above</li>
            <li>Verify the price ID matches one of the configured price IDs</li>
            <li>Ensure the subscription period hasn't expired</li>
            <li>Check the browser console for any error messages</li>
            <li>Try refreshing the page to clear any cached data</li>
            <li>If the issue persists, check the Stripe webhook logs</li>
          </ol>
        </div>
      </div>
    </main>
  );
}
