"use client";

import LoadingButton from "@/components/LoadingButton";
import { But<PERSON> } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { RefreshCw } from "lucide-react";
import { useState } from "react";
import { refreshSubscriptionFromStripe } from "./actions";

export default function RefreshSubscriptionButton() {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  async function handleRefresh() {
    try {
      setLoading(true);
      const result = await refreshSubscriptionFromStripe();
      
      if (result.success) {
        toast({
          title: "Success",
          description: result.message,
        });
        
        // Refresh the page to show updated data
        window.location.reload();
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: result.message,
        });
      }
    } catch (error) {
      console.error(error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to refresh subscription. Please try again.",
      });
    } finally {
      setLoading(false);
    }
  }

  return (
    <LoadingButton
      onClick={handleRefresh}
      loading={loading}
      variant="outline"
      size="sm"
    >
      <RefreshCw className="size-4 mr-2" />
      Refresh from Stripe
    </LoadingButton>
  );
}
