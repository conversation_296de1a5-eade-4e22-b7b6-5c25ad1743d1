import { env } from "@/env";
import prisma from "./prisma";

export type SubscriptionLevel = "free" | "pro" | "pro_plus";

export async function getUserSubscriptionLevel(userId: string): Promise<SubscriptionLevel> {
  console.log("🔍 Checking subscription for user:", userId);

  const subscription = await prisma.userSubscription.findUnique({
    where: {
      userId,
    },
  });

  console.log("📊 Subscription data:", subscription);

  if (!subscription) {
    console.log("❌ No subscription found - returning free");
    return "free";
  }

  if (subscription.stripeCurrentPeriodEnd < new Date()) {
    console.log("⏰ Subscription expired - returning free");
    return "free";
  }

  console.log("💰 Checking price ID:", subscription.stripePriceId);
  console.log("🎯 Pro monthly price ID:", env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_MONTHLY);
  console.log("🎯 Pro plus monthly price ID:", env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_PLUS_MONTHLY);

  if (subscription.stripePriceId === env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_MONTHLY) {
    console.log("✅ Pro subscription detected");
    return "pro";
  }

  if (subscription.stripePriceId === env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_PLUS_MONTHLY) {
    console.log("✅ Pro Plus subscription detected");
    return "pro_plus";
  }

  console.log("❓ Unknown price ID - returning free");
  return "free";
}
