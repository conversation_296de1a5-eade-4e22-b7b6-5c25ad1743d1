#!/usr/bin/env node

/**
 * Script to help configure development tunnel settings
 * Usage: node scripts/setup-tunnel.js <tunnel-url>
 * Example: node scripts/setup-tunnel.js https://x3p8lq66-3000.asse.devtunnels.ms
 */

const fs = require('fs');
const path = require('path');

const tunnelUrl = process.argv[2];

if (!tunnelUrl) {
  console.log('Usage: node scripts/setup-tunnel.js <tunnel-url>');
  console.log('Example: node scripts/setup-tunnel.js https://x3p8lq66-3000.asse.devtunnels.ms');
  process.exit(1);
}

// Validate URL format
try {
  new URL(tunnelUrl);
} catch (error) {
  console.error('Invalid URL format:', tunnelUrl);
  process.exit(1);
}

const envPath = path.join(process.cwd(), '.env');

// Read current .env file
let envContent = '';
try {
  envContent = fs.readFileSync(envPath, 'utf8');
} catch (error) {
  console.error('Could not read .env file:', error.message);
  process.exit(1);
}

// Update or add tunnel URL
const tunnelUrlLine = `NEXT_PUBLIC_TUNNEL_URL="${tunnelUrl}"`;
const tunnelUrlRegex = /^NEXT_PUBLIC_TUNNEL_URL=.*$/m;

if (tunnelUrlRegex.test(envContent)) {
  // Update existing line
  envContent = envContent.replace(tunnelUrlRegex, tunnelUrlLine);
  console.log('Updated existing NEXT_PUBLIC_TUNNEL_URL in .env');
} else {
  // Add new line
  envContent += `\n${tunnelUrlLine}\n`;
  console.log('Added NEXT_PUBLIC_TUNNEL_URL to .env');
}

// Also uncomment the tunnel URL line if it's commented
envContent = envContent.replace(/^# NEXT_PUBLIC_TUNNEL_URL=.*$/m, tunnelUrlLine);

// Write back to .env file
try {
  fs.writeFileSync(envPath, envContent);
  console.log('✅ Tunnel URL configured successfully!');
  console.log('🔄 Please restart your development server for changes to take effect.');
  console.log('📝 Tunnel URL set to:', tunnelUrl);
} catch (error) {
  console.error('Could not write to .env file:', error.message);
  process.exit(1);
}
