{"name": "nextjs-15-ai-resume-builder", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "postinstall": "prisma generate"}, "dependencies": {"@clerk/nextjs": "^6.2.1", "@clerk/themes": "^2.1.41", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@hookform/resolvers": "^3.9.1", "@prisma/client": "^5.22.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.2", "@t3-oss/env-nextjs": "^0.11.1", "@vercel/blob": "^0.26.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.455.0", "next": "15.0.3", "next-themes": "^0.4.3", "openai": "^4.71.1", "prisma": "^5.22.0", "react": "19.0.0-rc-66855b96-20241106", "react-color": "^2.19.3", "react-dom": "19.0.0-rc-66855b96-20241106", "react-hook-form": "^7.53.1", "react-to-print": "^3.0.2", "stripe": "^17.3.1", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zod": "^3.23.8", "zustand": "^5.0.1"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^18", "@types/react-color": "^3.0.13", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "15.0.3", "eslint-config-prettier": "^9.1.0", "postcss": "^8", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^3.4.1", "typescript": "^5"}}