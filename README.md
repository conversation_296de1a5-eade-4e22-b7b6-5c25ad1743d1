# Next.js 15 AI Resume Builder

Build and deploy a professional **full-stack SaaS** (software-as-a-service) application with **Stripe Checkout** and the **ChatGPT API**. 

Features:
- Multi-step form using React Hook Form
- Dynamic form arrays with useFieldArray
- Drag-and-drop with dnd-kit
- AI auto-fill
- Different subscription tiers
- Mobile responsive design with Tailwind CSS and Shadcn UI components
- Print or save as PDF using react-to-print
- URL state management
- Postgres DB and file uploads to Vercel Blob
- Auto-save hook
- & more

Watch the free **11-hour** tutorial on YouTube: https://www.youtube.com/watch?v=ySqesLjz6K0

![thumbnail](https://github.com/user-attachments/assets/f3eaef96-9674-4201-afeb-4deb3500ab6d)
