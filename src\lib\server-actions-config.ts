/**
 * Configuration for handling Server Actions with development tunnels
 * This helps resolve the "Invalid Server Actions request" error when using
 * development tunnels like VS Code dev tunnels, ngrok, etc.
 */

export function isValidTunnelOrigin(origin: string, forwardedHost: string): boolean {
  // List of known development tunnel domains
  const tunnelDomains = [
    'devtunnels.ms',
    'ngrok.io',
    'ngrok-free.app',
    'localhost.run',
    'localtunnel.me',
  ];

  // Check if the forwarded host contains any tunnel domain
  const isTunnelDomain = tunnelDomains.some(domain => 
    forwardedHost.includes(domain)
  );

  if (!isTunnelDomain) {
    return false;
  }

  // Extract the protocol and host from origin
  try {
    const originUrl = new URL(origin);
    const originHost = originUrl.host;

    // For development tunnels, we allow:
    // 1. localhost origins when forwarded host is a tunnel
    // 2. 127.0.0.1 origins when forwarded host is a tunnel
    // 3. The tunnel domain itself
    return (
      originHost === 'localhost:3000' ||
      originHost === '127.0.0.1:3000' ||
      originHost === forwardedHost ||
      originHost.includes('localhost') ||
      originHost.includes('127.0.0.1')
    );
  } catch {
    return false;
  }
}

export function getAllowedOrigins(): string[] {
  const baseOrigins = [
    'http://localhost:3000',
    'https://localhost:3000',
    'http://127.0.0.1:3000',
    'https://127.0.0.1:3000',
  ];

  // Add tunnel origins from environment if available
  const tunnelUrl = process.env.NEXT_PUBLIC_TUNNEL_URL;
  if (tunnelUrl) {
    baseOrigins.push(tunnelUrl);
  }

  return baseOrigins;
}
