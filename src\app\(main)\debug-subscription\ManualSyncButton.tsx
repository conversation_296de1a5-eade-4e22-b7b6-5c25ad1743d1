"use client";

import LoadingButton from "@/components/LoadingButton";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Settings } from "lucide-react";
import { useState } from "react";

export default function ManualSyncButton({ userId }: { userId: string }) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);

  async function handleManualSync() {
    try {
      setLoading(true);
      
      // Call our manual sync API
      const response = await fetch('/api/manual-sync-subscription', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ userId }),
      });

      const result = await response.json();
      
      if (result.success) {
        toast({
          title: "Success",
          description: result.message,
        });
        
        // Refresh the page to show updated data
        window.location.reload();
      } else {
        toast({
          variant: "destructive",
          title: "Error",
          description: result.message,
        });
      }
    } catch (error) {
      console.error(error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to sync subscription. Please try again.",
      });
    } finally {
      setLoading(false);
    }
  }

  return (
    <LoadingButton
      onClick={handleManualSync}
      loading={loading}
      variant="outline"
      size="sm"
    >
      <Settings className="size-4 mr-2" />
      Manual Sync
    </LoadingButton>
  );
}
