import { clerkMiddleware, createRouteMatcher } from "@clerk/nextjs/server";
import { NextResponse } from "next/server";

const isPublicRoute = createRouteMatcher([
  "/",
  "/sign-in(.*)",
  "/sign-up(.*)",
  "/api/stripe-webhook",
]);

export default clerkMiddleware(async (auth, request) => {
  // Handle development tunnel headers for Server Actions
  const response = NextResponse.next();

  // Get the forwarded host header (from tunnels like VS Code dev tunnels, ngrok, etc.)
  const forwardedHost = request.headers.get("x-forwarded-host");
  const origin = request.headers.get("origin");

  // If we have a forwarded host and it's a tunnel domain, allow it
  if (forwardedHost && origin) {
    const isTunnelDomain =
      forwardedHost.includes("devtunnels.ms") ||
      forwardedHost.includes("ngrok.io") ||
      forwardedHost.includes("ngrok-free.app");

    if (isTunnelDomain) {
      // Set headers to allow the tunnel origin for Server Actions
      response.headers.set("Access-Control-Allow-Origin", origin);
      response.headers.set("Access-Control-Allow-Credentials", "true");
      response.headers.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
      response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization");
    }
  }

  if (!isPublicRoute(request)) {
    await auth.protect();
  }

  return response;
});

export const config = {
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)",
    // Always run for API routes
    "/(api|trpc)(.*)",
  ],
};
