# Development Tunnel Setup

This document explains how to resolve the "Invalid Server Actions request" error when using development tunnels with this Next.js application.

## Problem

When using development tunnels (VS Code dev tunnels, ngrok, etc.), you might encounter this error:

```
`x-forwarded-host` header with value `x3p8lq66-3000.asse.devtunnels.ms` does not match `origin` header with value `localhost:3000` from a forwarded Server Actions request. Aborting the action.
```

This happens because Next.js 15 has stricter origin checking for Server Actions to prevent CSRF attacks.

## Solution

### Automatic Setup (Recommended)

1. **Get your tunnel URL** from your tunnel service (VS Code dev tunnels, ngrok, etc.)
   
2. **Run the setup script**:
   ```bash
   node scripts/setup-tunnel.js https://your-tunnel-url.devtunnels.ms
   ```

3. **Restart your development server**:
   ```bash
   npm run dev
   ```

### Manual Setup

1. **Update your `.env` file**:
   ```env
   # Uncomment and set your tunnel URL
   NEXT_PUBLIC_TUNNEL_URL="https://your-tunnel-url.devtunnels.ms"
   ```

2. **Restart your development server**

## Supported Tunnel Services

The configuration automatically supports these tunnel services:

- **VS Code Dev Tunnels** (`*.devtunnels.ms`)
- **ngrok** (`*.ngrok.io`, `*.ngrok-free.app`)
- **localhost.run** (`*.localhost.run`)
- **localtunnel** (`*.localtunnel.me`)

## How It Works

The fix works by:

1. **Next.js Configuration**: Updated `next.config.ts` to allow tunnel origins in Server Actions
2. **Middleware**: Enhanced middleware to handle tunnel headers properly
3. **Environment Variables**: Added support for configuring specific tunnel URLs

## Troubleshooting

### Still getting the error?

1. **Check your tunnel URL** is correctly set in `.env`
2. **Restart the development server** after making changes
3. **Clear browser cache** and try again
4. **Check the console** for any additional error messages

### For VS Code Dev Tunnels

1. Open Command Palette (`Ctrl+Shift+P` or `Cmd+Shift+P`)
2. Run "Ports: Focus on Ports View"
3. Right-click on port 3000 and select "Port Visibility" → "Public"
4. Copy the tunnel URL and use it with the setup script

### For ngrok

1. Start ngrok: `ngrok http 3000`
2. Copy the HTTPS URL (e.g., `https://abc123.ngrok.io`)
3. Use it with the setup script

## Security Note

This configuration is only for development environments. The tunnel origin checking is automatically disabled in production builds.

## Need Help?

If you're still experiencing issues:

1. Check that your tunnel service is running
2. Verify the tunnel URL is accessible in a browser
3. Ensure the development server is running on port 3000
4. Try clearing browser cache and cookies
