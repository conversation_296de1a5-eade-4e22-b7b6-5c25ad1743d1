#!/usr/bin/env node

/**
 * Script to test subscription functionality
 * This helps debug subscription issues by checking the database directly
 */

const { PrismaClient } = require('@prisma/client');

async function testSubscription() {
  const prisma = new PrismaClient();

  try {
    console.log('🔍 Testing subscription functionality...\n');

    // Get all subscriptions
    const subscriptions = await prisma.userSubscription.findMany();
    
    console.log(`📊 Found ${subscriptions.length} subscription(s) in database:\n`);

    if (subscriptions.length === 0) {
      console.log('❌ No subscriptions found in database');
      console.log('💡 This could mean:');
      console.log('   - No one has subscribed yet');
      console.log('   - Stripe webhooks are not working');
      console.log('   - Database connection issues');
      return;
    }

    subscriptions.forEach((sub, index) => {
      console.log(`Subscription ${index + 1}:`);
      console.log(`  User ID: ${sub.userId}`);
      console.log(`  Stripe Customer ID: ${sub.stripeCustomerId}`);
      console.log(`  Stripe Subscription ID: ${sub.stripeSubscriptionId}`);
      console.log(`  Price ID: ${sub.stripePriceId}`);
      console.log(`  Current Period End: ${sub.stripeCurrentPeriodEnd}`);
      console.log(`  Cancel at Period End: ${sub.stripeCancelAtPeriodEnd}`);
      console.log(`  Created: ${sub.createdAt}`);
      console.log(`  Updated: ${sub.updatedAt}`);
      
      // Check if subscription is active
      const isActive = sub.stripeCurrentPeriodEnd > new Date();
      console.log(`  Status: ${isActive ? '✅ Active' : '❌ Expired'}`);
      
      console.log('');
    });

    // Check environment variables
    console.log('🔧 Environment Variables:');
    console.log(`  Pro Monthly Price ID: ${process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_MONTHLY || 'NOT SET'}`);
    console.log(`  Pro Plus Monthly Price ID: ${process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_PLUS_MONTHLY || 'NOT SET'}`);
    console.log('');

    // Check for price ID matches
    subscriptions.forEach((sub, index) => {
      console.log(`Price ID Analysis for Subscription ${index + 1}:`);
      const proMatch = sub.stripePriceId === process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_MONTHLY;
      const proPlusMatch = sub.stripePriceId === process.env.NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_PLUS_MONTHLY;
      
      console.log(`  Matches Pro: ${proMatch ? '✅ Yes' : '❌ No'}`);
      console.log(`  Matches Pro Plus: ${proPlusMatch ? '✅ Yes' : '❌ No'}`);
      
      if (!proMatch && !proPlusMatch) {
        console.log(`  ⚠️  Unknown price ID: ${sub.stripePriceId}`);
        console.log(`     This subscription won't be recognized as premium`);
      }
      console.log('');
    });

  } catch (error) {
    console.error('❌ Error testing subscription:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// Run the test
testSubscription();
