# Subscription Troubleshooting Guide

This guide helps you troubleshoot subscription issues where users remain on the "free" plan even after subscribing.

## Quick Diagnosis

### 1. Check Subscription Status
Visit `/debug-subscription` in your app to see detailed subscription information.

### 2. Run Database Test
```bash
npm run test-subscription
```

### 3. Check Browser Console
Look for subscription-related log messages in the browser console.

## Common Issues and Solutions

### Issue 1: React Cache Not Clearing

**Symptoms:**
- User shows as "free" even after successful payment
- Database shows correct subscription data
- Issue persists until server restart

**Solution:**
✅ **Fixed** - Removed React `cache()` from `getUserSubscriptionLevel` function

### Issue 2: Stripe Webhooks Not Working

**Symptoms:**
- No subscription data in database after payment
- Stripe shows successful payment but app doesn't recognize it

**Diagnosis:**
1. Check Stripe webhook endpoint configuration
2. Verify webhook secret in environment variables
3. Check webhook delivery logs in Stripe dashboard

**Solution:**
```bash
# Check webhook configuration
curl -X GET https://api.stripe.com/v1/webhook_endpoints \
  -H "Authorization: Bearer sk_test_..."
```

### Issue 3: Price ID Mismatch

**Symptoms:**
- Subscription exists in database
- Price ID doesn't match environment variables
- User still shows as "free"

**Diagnosis:**
Check the debug page for price ID comparison.

**Solution:**
1. Verify price IDs in Stripe dashboard
2. Update environment variables:
   ```env
   NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_MONTHLY="price_xxx"
   NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_PLUS_MONTHLY="price_yyy"
   ```

### Issue 4: Subscription Expired

**Symptoms:**
- Subscription exists but user shows as "free"
- `stripeCurrentPeriodEnd` is in the past

**Solution:**
This is expected behavior. Check Stripe for renewal status.

## Debugging Tools

### 1. Debug Page
Visit `/debug-subscription` to see:
- Current subscription level
- Database subscription data
- Price ID comparisons
- Period validity

### 2. Refresh Subscription
Use the "Refresh from Stripe" button to manually sync subscription data.

### 3. Console Logs
The subscription function now includes detailed logging:
```
🔍 Checking subscription for user: user_xxx
📊 Subscription data: {...}
💰 Checking price ID: price_xxx
✅ Pro subscription detected
```

### 4. Database Test Script
```bash
npm run test-subscription
```

## Manual Fix Steps

### Step 1: Verify Stripe Data
1. Go to Stripe dashboard
2. Find the customer
3. Check active subscriptions
4. Note the price ID

### Step 2: Check Database
```sql
SELECT * FROM user_subscriptions WHERE userId = 'user_xxx';
```

### Step 3: Manual Sync
1. Visit `/debug-subscription`
2. Click "Refresh from Stripe"
3. Check if subscription updates

### Step 4: Environment Variables
Verify these are set correctly:
```env
NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_MONTHLY="price_xxx"
NEXT_PUBLIC_STRIPE_PRICE_ID_PRO_PLUS_MONTHLY="price_yyy"
STRIPE_SECRET_KEY="sk_test_xxx"
STRIPE_WEBHOOK_SECRET="whsec_xxx"
```

## Prevention

### 1. Webhook Monitoring
Set up monitoring for webhook failures:
- Check Stripe webhook logs regularly
- Set up alerts for failed webhooks

### 2. Testing
- Test subscription flow in development
- Verify webhook endpoints are accessible
- Test with Stripe test cards

### 3. Logging
The updated code includes comprehensive logging for easier debugging.

## Emergency Recovery

If a user's subscription is stuck:

1. **Immediate Fix:**
   ```bash
   # Run the refresh action
   curl -X POST https://your-app.com/debug-subscription/refresh
   ```

2. **Manual Database Update:**
   ```sql
   UPDATE user_subscriptions 
   SET stripePriceId = 'correct_price_id',
       stripeCurrentPeriodEnd = 'future_date'
   WHERE userId = 'user_id';
   ```

3. **Force Page Refresh:**
   Ask user to hard refresh (Ctrl+F5) or clear browser cache.

## Getting Help

If issues persist:

1. Check the browser console for errors
2. Review Stripe webhook logs
3. Run the database test script
4. Check the debug page for detailed information
5. Verify all environment variables are correct

## Recent Changes

- ✅ Removed React cache from subscription function
- ✅ Added comprehensive logging
- ✅ Added webhook revalidation
- ✅ Created debug tools and refresh functionality
- ✅ Added manual sync capabilities
