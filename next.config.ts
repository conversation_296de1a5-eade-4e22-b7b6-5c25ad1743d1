import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  experimental: {
    serverActions: {
      bodySizeLimit: "4mb",
      // Allow Server Actions to work with development tunnels
      allowedOrigins: [
        "localhost:3000",
        "127.0.0.1:3000",
        // Development tunnel domains
        "*.devtunnels.ms",
        "*.ngrok.io",
        "*.ngrok-free.app",
        "*.localhost.run",
        "*.localtunnel.me",
        // Add specific tunnel URL from environment
        ...(process.env.NEXT_PUBLIC_TUNNEL_URL ? [process.env.NEXT_PUBLIC_TUNNEL_URL] : []),
      ],
    },
  },
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "jse2rpa8arnz4aaz.public.blob.vercel-storage.com"
      }
    ]
  },
  // Handle forwarded headers for development tunnels
  async headers() {
    return [
      {
        source: "/(.*)",
        headers: [
          {
            key: "X-Frame-Options",
            value: "DENY",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
        ],
      },
    ];
  },
};

export default nextConfig;
