"use server";

import { env } from "@/env";
import prisma from "@/lib/prisma";
import stripe from "@/lib/stripe";
import { auth, currentUser } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";

export async function refreshSubscriptionFromStripe() {
  const { userId } = await auth();
  const user = await currentUser();

  if (!userId || !user) {
    throw new Error("Not authenticated");
  }

  console.log("🔄 Manually refreshing subscription for user:", userId);

  // Get the Stripe customer ID from Clerk metadata
  const stripeCustomerId = user.privateMetadata.stripeCustomerId as string | undefined;

  if (!stripeCustomerId) {
    console.log("❌ No Stripe customer ID found in user metadata");
    return { success: false, message: "No Stripe customer ID found" };
  }

  try {
    // Get all subscriptions for this customer from Stripe
    const subscriptions = await stripe.subscriptions.list({
      customer: stripeCustomerId,
      status: "all",
      limit: 10,
    });

    console.log("📋 Found subscriptions from Stripe:", subscriptions.data.length);

    // Find the most recent active subscription
    const activeSubscription = subscriptions.data.find(
      sub => sub.status === "active" || sub.status === "trialing" || sub.status === "past_due"
    );

    if (activeSubscription) {
      console.log("✅ Found active subscription:", activeSubscription.id);

      // Update the database with the latest subscription data
      const result = await prisma.userSubscription.upsert({
        where: {
          userId,
        },
        create: {
          userId,
          stripeSubscriptionId: activeSubscription.id,
          stripeCustomerId: stripeCustomerId,
          stripePriceId: activeSubscription.items.data[0].price.id,
          stripeCurrentPeriodEnd: new Date(activeSubscription.current_period_end * 1000),
          stripeCancelAtPeriodEnd: activeSubscription.cancel_at_period_end,
        },
        update: {
          stripeSubscriptionId: activeSubscription.id,
          stripePriceId: activeSubscription.items.data[0].price.id,
          stripeCurrentPeriodEnd: new Date(activeSubscription.current_period_end * 1000),
          stripeCancelAtPeriodEnd: activeSubscription.cancel_at_period_end,
        },
      });

      console.log("💾 Updated subscription in database:", result);

      // Revalidate all relevant pages
      revalidatePath("/billing");
      revalidatePath("/editor");
      revalidatePath("/resumes");
      revalidatePath("/debug-subscription");

      return { 
        success: true, 
        message: "Subscription refreshed successfully",
        subscription: result
      };
    } else {
      console.log("❌ No active subscription found");

      // Remove any existing subscription from database
      await prisma.userSubscription.deleteMany({
        where: { userId },
      });

      // Revalidate pages
      revalidatePath("/billing");
      revalidatePath("/editor");
      revalidatePath("/resumes");
      revalidatePath("/debug-subscription");

      return { 
        success: true, 
        message: "No active subscription found - removed from database" 
      };
    }
  } catch (error) {
    console.error("❌ Error refreshing subscription:", error);
    return { 
      success: false, 
      message: `Error: ${error instanceof Error ? error.message : "Unknown error"}` 
    };
  }
}

export async function checkStripeWebhookEndpoint() {
  try {
    // List webhook endpoints to verify configuration
    const endpoints = await stripe.webhookEndpoints.list();
    
    const relevantEndpoint = endpoints.data.find(endpoint => 
      endpoint.url.includes(env.NEXT_PUBLIC_BASE_URL) || 
      endpoint.url.includes("localhost") ||
      endpoint.url.includes("devtunnels.ms")
    );

    return {
      success: true,
      endpoint: relevantEndpoint,
      allEndpoints: endpoints.data.map(ep => ({
        id: ep.id,
        url: ep.url,
        status: ep.status,
        events: ep.enabled_events,
      })),
    };
  } catch (error) {
    return {
      success: false,
      message: `Error: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}
