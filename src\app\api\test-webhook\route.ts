import { NextRequest } from "next/server";

export async function POST(req: NextRequest) {
  console.log("🧪 Test webhook endpoint hit!");
  
  try {
    const body = await req.text();
    const headers = Object.fromEntries(req.headers.entries());
    
    console.log("📋 Webhook test data:");
    console.log("Headers:", headers);
    console.log("Body length:", body.length);
    console.log("Body preview:", body.substring(0, 200));
    
    return new Response("Test webhook received successfully", { status: 200 });
  } catch (error) {
    console.error("❌ Test webhook error:", error);
    return new Response("Test webhook error", { status: 500 });
  }
}

export async function GET() {
  return new Response("Test webhook endpoint is working", { status: 200 });
}
